#!/usr/bin/env python3
"""
DeepSeek R1 - Single Entry Point
This file serves as the main entry point for the DeepSeek R1 application.
It checks dependencies, starts Ollama if needed, and launches the main GUI.
"""
import os
import sys
import subprocess
import time
import importlib.util
import threading

def check_and_install_dependencies():
    """Check if required dependencies are installed and install them if needed."""
    print("Checking dependencies...")
    
    # List of required packages
    required_packages = [
        "requests",
        "psutil",
        "tkinter"
    ]
    
    # Check if each package is installed
    missing_packages = []
    for package in required_packages:
        try:
            if package == "tkinter":
                # Special case for tkinter which is part of the standard library
                import tkinter
                print(f"✓ {package} is installed")
            else:
                __import__(package)
                print(f"✓ {package} is installed")
        except ImportError:
            print(f"✗ {package} is not installed")
            missing_packages.append(package)
    
    # Install missing packages
    if missing_packages:
        print("\nInstalling missing packages...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing_packages)
            print("All dependencies installed successfully!")
        except subprocess.CalledProcessError as e:
            print(f"Error installing dependencies: {e}")
            print("\nPlease try to install them manually using:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    else:
        print("\nAll dependencies are already installed!")
    
    # Check if Ollama is installed
    print("\nChecking if Ollama is installed...")
    try:
        result = subprocess.run(['ollama', 'list'], 
                              capture_output=True, 
                              text=True, 
                              encoding='utf-8')
        if result.returncode == 0:
            print("✓ Ollama is installed")
        else:
            print("✗ Ollama is not installed or not working properly")
            print("Please install Ollama from: https://ollama.com/download/windows")
            return False
    except FileNotFoundError:
        print("✗ Ollama is not installed or not in PATH")
        print("Please install Ollama from: https://ollama.com/download/windows")
        return False
    
    print("\nDependency check completed!")
    return True

def start_ollama():
    """Start Ollama if it's not already running."""
    print("\nChecking if Ollama is running...")
    
    # Import the Ollama module
    try:
        from Ollama import OllamaManager
        
        # Check if Ollama is already running
        if OllamaManager.check_ollama_running():
            print("✓ Ollama is already running")
            return True
        
        # Start Ollama
        print("Starting Ollama...")
        if OllamaManager.start_ollama():
            print("✓ Ollama started successfully")
            return True
        else:
            print("✗ Failed to start Ollama")
            return False
    except ImportError:
        print("✗ Ollama module not found")
        return False

def start_api_server():
    """Start the API server in the background."""
    print("\nStarting API server...")
    
    # Import the Ollama module
    try:
        from Ollama import start_api_server_in_background
        
        # Start the API server
        api_thread = start_api_server_in_background()
        if api_thread:
            print("✓ API server started successfully")
            return True
        else:
            print("✗ API server may already be running")
            return True  # Return True anyway since it might be running
    except ImportError:
        print("✗ Ollama module not found")
        return False

def launch_main_application():
    """Launch the main DeepSeek R1 application."""
    print("\nLaunching DeepSeek R1...")
    
    # Check if the main launcher file exists
    if not os.path.exists("deepseek_r1_launcher.py"):
        print("✗ Main application file not found")
        return False
    
    # Import the main module directly
    try:
        # Add the current directory to the path
        sys.path.insert(0, os.path.abspath('.'))
        
        # Import the main module
        import deepseek_r1_launcher
        
        # Get the DeepSeekLauncher class
        DeepSeekLauncher = deepseek_r1_launcher.DeepSeekLauncher
        
        # Import tkinter
        import tkinter as tk
        
        # Create the root window
        root = tk.Tk()
        root.title("DeepSeek R1")
        
        # Try to set the icon
        try:
            root.iconbitmap("icon.ico")
        except:
            pass
        
        # Create the application instance
        app = DeepSeekLauncher(root)
        
        # Start the main event loop
        print("✓ Application started successfully")
        root.mainloop()
        
        return True
    except Exception as e:
        print(f"✗ Error launching application: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to launch the DeepSeek R1 application."""
    print("DeepSeek R1 Launcher")
    print("====================")
    
    # Check dependencies
    if not check_and_install_dependencies():
        print("\nWarning: Dependency check failed. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")
    
    # Start Ollama
    if not start_ollama():
        print("\nWarning: Failed to start Ollama. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")
    
    # Start API server
    if not start_api_server():
        print("\nWarning: Failed to start API server. The application may not work correctly.")
        input("Press Enter to continue anyway or Ctrl+C to exit...")
    
    # Launch the main application
    if not launch_main_application():
        print("\nError: Failed to launch the main application.")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
