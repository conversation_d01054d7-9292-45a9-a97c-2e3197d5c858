import tkinter as tk
from tkinter import scrolledtext, messagebox, ttk
import threading
import subprocess
import requests
import json
import os
import sys

class DeepSeekR1GUI:
    def __init__(self, root):
        self.root = root
        self.root.title("AI Chat GUI")
        self.root.geometry("800x600")
        self.root.minsize(800, 600)

        # Set icon if available
        # self.root.iconbitmap("icon.ico")

        # Model status
        self.model_running = False
        self.model_process = None
        self.model_name = tk.StringVar(value="deepseek-r1:1.5b")

        # Check GPU availability
        self.check_gpu()

        # Create main frame
        main_frame = ttk.Frame(root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create model selection frame
        model_frame = ttk.LabelFrame(main_frame, text="Model Selection", padding=10)
        model_frame.pack(fill=tk.X, pady=5)

        # Model selection dropdown
        ttk.Label(model_frame, text="Select Model:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.model_combo = ttk.Combobox(model_frame, textvariable=self.model_name, width=30)
        self.model_combo['values'] = ('deepseek-r1:1.5b', 'deepseek-r1:7b-q4_0')
        self.model_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Start/Stop button
        self.start_stop_button = ttk.Button(model_frame, text="Start Model", command=self.toggle_model)
        self.start_stop_button.grid(row=0, column=2, padx=5, pady=5)

        # Refresh models button
        refresh_button = ttk.Button(model_frame, text="Refresh Models", command=self.refresh_models)
        refresh_button.grid(row=0, column=3, padx=5, pady=5)

        # Create chat frame
        chat_frame = ttk.LabelFrame(main_frame, text="Chat", padding=10)
        chat_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Chat history
        self.chat_history = scrolledtext.ScrolledText(chat_frame, wrap=tk.WORD, height=15)
        self.chat_history.pack(fill=tk.BOTH, expand=True, pady=5)
        self.chat_history.config(state=tk.DISABLED)

        # Display any pending messages
        if hasattr(self, 'pending_messages'):
            for timestamp, sender, message in self.pending_messages:
                self.chat_history.config(state=tk.NORMAL)
                formatted_message = f"[{timestamp}] {sender}:\n{message}\n\n"
                self.chat_history.insert(tk.END, formatted_message)
                self.chat_history.config(state=tk.DISABLED)

            # Auto-scroll to bottom
            self.chat_history.see(tk.END)

            # Clear pending messages
            self.pending_messages = []

        # User input
        input_frame = ttk.Frame(chat_frame)
        input_frame.pack(fill=tk.X, pady=5)

        # Create a label frame with a hint
        input_label_frame = ttk.LabelFrame(input_frame, text="Type your message (click 'Start Model' first)")
        input_label_frame.pack(fill=tk.X, side=tk.LEFT, expand=True, padx=5)
        self.input_label_frame = input_label_frame

        self.user_input = scrolledtext.ScrolledText(input_label_frame, wrap=tk.WORD, height=3)
        self.user_input.pack(fill=tk.BOTH, expand=True)
        self.user_input.bind("<Shift-Return>", lambda _: None)
        self.user_input.bind("<Return>", self.send_message)

        # Add a hint to the text box with grey color to indicate it's a placeholder
        self.user_input.insert(tk.END, "Type your message here and press Enter to send...")
        self.user_input.config(foreground="grey")
        self.user_input.bind("<FocusIn>", self._on_entry_click)
        self.user_input.bind("<FocusOut>", self._on_focus_out)
        self.entry_is_default = True

        # Send button with better styling
        send_button = ttk.Button(input_frame, text="Send", command=lambda: self.send_message(None), width=10)
        send_button.pack(side=tk.RIGHT, padx=5, pady=5)

        # Status bar
        self.status_var = tk.StringVar(value="Status: Model not running")
        status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Initialize
        self.refresh_models()

        # Add a status indicator frame below the model selection
        status_indicator_frame = ttk.Frame(model_frame)
        status_indicator_frame.grid(row=1, column=0, columnspan=4, sticky="w", padx=5, pady=(5, 0))

        # Add a colored indicator
        self.status_indicator = ttk.Label(status_indicator_frame, text="●", font=("Arial", 12, "bold"), foreground="red")
        self.status_indicator.pack(side=tk.LEFT, padx=(0, 5))

        # Add a status text
        self.status_text = ttk.Label(status_indicator_frame, text="Model not running. Click 'Start Model' or type a message to begin.")
        self.status_text.pack(side=tk.LEFT)

        # Check if Ollama is running and if models are available silently
        if self.check_ollama_running():
            # Check if any models are available
            try:
                result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
                if result.returncode == 0:
                    models = []
                    for line in result.stdout.strip().split('\n')[1:]:  # Skip header
                        if line.strip():
                            model_name = line.split()[0]
                            # Show all models, not just DeepSeek R1
                            models.append(model_name)

                    if models:
                        # Update the model dropdown silently
                        self.model_combo['values'] = tuple(models)
                        # Update status text
                        self.status_text.config(text=f"Found {len(models)} AI models. Ready to start.")
            except Exception:
                pass

    def check_gpu(self):
        """Check if GPU is available"""
        try:
            # Try to run nvidia-smi to check for GPU
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                # GPU is available
                # Extract GPU info
                lines = result.stdout.strip().split('\n')
                gpu_info = ""
                for line in lines:
                    if "NVIDIA GeForce RTX" in line or "NVIDIA RTX" in line:
                        gpu_info = line.strip()
                        break

                if gpu_info:
                    self.append_to_chat("System", f"GPU detected: {gpu_info}")
                else:
                    self.append_to_chat("System", "NVIDIA GPU detected.")
            else:
                # No GPU or nvidia-smi not available
                self.append_to_chat("System", "Warning: No GPU detected. DeepSeek R1 will run very slowly on CPU.")
        except FileNotFoundError:
            # nvidia-smi not found
            self.append_to_chat("System", "Warning: NVIDIA GPU drivers not found. DeepSeek R1 will run very slowly on CPU.")
        except Exception:
            # Error checking GPU
            self.append_to_chat("System", "Could not detect GPU. DeepSeek R1 may run slowly if no GPU is available.")

    def refresh_models(self):
        """Refresh the list of available models"""
        try:
            # Check if Ollama is installed
            try:
                result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            except FileNotFoundError:
                # Ollama is not installed or not in PATH
                if hasattr(self, 'status_text'):
                    self.status_text.config(text="Ollama not found. Please install Ollama first.")
                    self.status_indicator.config(foreground="red")
                return

            if result.returncode == 0:
                models = []
                for line in result.stdout.strip().split('\n')[1:]:  # Skip header
                    if line.strip():
                        model_name = line.split()[0]
                        # Show all models, not just DeepSeek R1
                        models.append(model_name)

                if models:
                    self.model_combo['values'] = tuple(models)
                    if hasattr(self, 'status_text'):
                        self.status_text.config(text=f"Found {len(models)} AI models. Ready to start.")
                else:
                    if hasattr(self, 'status_text'):
                        self.status_text.config(text="No AI models found. Please download a model.")
                        self.status_indicator.config(foreground="orange")
            else:
                # Ollama is not running
                if hasattr(self, 'status_text'):
                    self.status_text.config(text="Ollama is not running. Please start Ollama first.")
                    self.status_indicator.config(foreground="red")
        except Exception as e:
            if hasattr(self, 'status_text'):
                self.status_text.config(text=f"Error refreshing models: {str(e)}")
                self.status_indicator.config(foreground="red")

    def install_ollama(self):
        """Install Ollama"""
        self.status_var.set("Status: Installing Ollama...")

        # Create a batch file to install Ollama
        with open("install_ollama.bat", "w") as f:
            f.write("@echo off\n")
            f.write("echo ===================================================\n")
            f.write("echo Installing Ollama\n")
            f.write("echo ===================================================\n")
            f.write("echo.\n")
            f.write("echo Downloading Ollama...\n")
            f.write("curl -L -o OllamaSetup.exe https://ollama.com/download/OllamaSetup.exe\n")
            f.write("echo.\n")
            f.write("echo Installing Ollama...\n")
            f.write("echo Please follow the installation instructions in the installer window.\n")
            f.write("echo.\n")
            f.write("start /wait OllamaSetup.exe\n")
            f.write("echo.\n")
            f.write("echo Starting Ollama...\n")
            f.write("start ollama serve\n")
            f.write("echo.\n")
            f.write("echo ===================================================\n")
            f.write("echo Installation complete.\n")
            f.write("echo ===================================================\n")
            f.write("echo.\n")
            f.write("echo Press any key to exit...\n")
            f.write("pause > nul")

        # Run the batch file
        messagebox.showinfo("Installing Ollama", "Ollama will now be installed. Please follow the instructions in the installer window.")
        subprocess.Popen(["cmd", "/c", "start", "cmd", "/k", "install_ollama.bat"])

        # Update status
        self.status_var.set("Status: Ollama installation started. Please restart the GUI after installation.")

    def start_ollama(self):
        """Start Ollama"""
        self.status_var.set("Status: Starting Ollama...")

        # Create a batch file to start Ollama
        with open("start_ollama_temp.bat", "w") as f:
            f.write("@echo off\n")
            f.write("echo ===================================================\n")
            f.write("echo Starting Ollama\n")
            f.write("echo ===================================================\n")
            f.write("echo.\n")
            f.write("echo Starting Ollama...\n")
            f.write("start ollama serve\n")
            f.write("echo.\n")
            f.write("echo Ollama started.\n")
            f.write("echo.\n")
            f.write("echo Press any key to exit...\n")
            f.write("pause > nul")

        # Run the batch file
        subprocess.Popen(["cmd", "/c", "start", "cmd", "/k", "start_ollama_temp.bat"])

        # Update status
        self.status_var.set("Status: Ollama starting. Please wait a moment and then refresh models.")

        # Wait a moment and then refresh models
        self.root.after(5000, self.refresh_models)

    def toggle_model(self):
        """Start or stop the model"""
        if self.model_running:
            self.stop_model()
        else:
            self.start_model()

    def check_ollama_running(self):
        """Check if Ollama is running"""
        try:
            # Try to connect to Ollama API
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            return response.status_code == 200
        except:
            return False

    def start_model(self):
        """Start the selected model"""
        model = self.model_name.get()

        try:
            # Check if Ollama is running
            if not self.check_ollama_running():
                start_ollama = messagebox.askyesno("Ollama Not Running",
                                                "Ollama is not running. \n\nDo you want to start Ollama now?")
                if start_ollama:
                    self.start_ollama()
                    # Wait a bit for Ollama to start
                    self.status_var.set("Status: Starting Ollama...")
                    self.root.after(5000, lambda: self.start_model())
                    return
                else:
                    return

            # Check if model exists
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                models = [line.split()[0] for line in result.stdout.strip().split('\n')[1:] if line.strip()]
                if model not in models:
                    # Model not found, ask to download
                    download = messagebox.askyesno("Model Not Found",
                                                 f"Model '{model}' not found. Do you want to download it?")
                    if download:
                        self.download_model(model)
                    return

            # Start model in a separate thread
            self.status_var.set(f"Status: Starting {model}...")
            threading.Thread(target=self._run_model, args=(model,), daemon=True).start()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start model: {str(e)}")

    def _run_model(self, model):
        """Run the model using the Ollama API"""
        try:
            # Check if the API is available
            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=2)
                if response.status_code != 200:
                    self.root.after(0, lambda: messagebox.showerror("Error", f"Ollama API error: {response.status_code}"))
                    self.root.after(0, self._update_ui_stopped)
                    return
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Error", f"Ollama API not available: {str(e)}"))
                self.root.after(0, self._update_ui_stopped)
                return

            # Update UI to show model is running
            self.root.after(0, self._update_ui_running)

            # Send a test message to the model to verify it works
            try:
                url = "http://localhost:11434/api/generate"
                payload = {
                    "model": model,
                    "prompt": "Say hello and introduce yourself briefly.",
                    "stream": False
                }

                response = requests.post(url, json=payload, timeout=30)

                if response.status_code == 200:
                    result = response.json()
                    self.root.after(0, lambda: self.append_to_chat("AI",
                                                               "Hello! I'm ready to chat. " + result["response"]))
                else:
                    self.root.after(0, self._update_ui_stopped)
                    self.root.after(0, lambda: messagebox.showerror("Error",
                                                                 f"Failed to start model: API returned {response.status_code}"))
                    return

            except Exception as e:
                self.root.after(0, self._update_ui_stopped)
                self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to communicate with model: {str(e)}"))
                return

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to run model: {str(e)}"))
            self.root.after(0, self._update_ui_stopped)

    def _update_ui_running(self):
        """Update UI when model is running"""
        self.model_running = True
        self.start_stop_button.config(text="Stop Model")
        self.status_var.set(f"Status: Running {self.model_name.get()}")
        self.append_to_chat("System", "Model started. You can now chat with the AI!")

        # Update input label frame text
        self.input_label_frame.config(text="Type your message (model is running)")

        # Update status indicator
        if hasattr(self, 'status_indicator'):
            self.status_indicator.config(foreground="green")
            self.status_text.config(text=f"Model {self.model_name.get()} is running. Ready to chat!")

        # Send pending message if there is one
        if hasattr(self, '_pending_user_message') and self._pending_user_message:
            # Display user message
            self.append_to_chat("You", self._pending_user_message)

            # Get response from model
            threading.Thread(target=self._get_model_response, args=(self._pending_user_message,), daemon=True).start()

            # Clear pending message
            self._pending_user_message = None

    def _update_ui_stopped(self):
        """Update UI when model is stopped"""
        self.model_running = False
        self.model_process = None
        self.start_stop_button.config(text="Start Model")
        self.status_var.set("Status: Model not running")
        self.append_to_chat("System", "Model stopped.")

        # Update input label frame text
        self.input_label_frame.config(text="Type your message (click 'Start Model' first)")

        # Update status indicator
        if hasattr(self, 'status_indicator'):
            self.status_indicator.config(foreground="red")
            self.status_text.config(text="Model not running. Click 'Start Model' or type a message to begin.")

    def stop_model(self):
        """Stop the running model"""
        try:
            # Just update the UI to show the model is stopped
            # The API doesn't need to be explicitly stopped
            self._update_ui_stopped()
            self.status_var.set("Status: Model stopped")
            self.append_to_chat("System", "Model stopped.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop model: {str(e)}")

    def download_model(self, model):
        """Download the specified model"""
        self.status_var.set(f"Status: Downloading {model}...")

        # Disable buttons during download
        self.start_stop_button.config(state=tk.DISABLED)

        # Start download in a separate thread
        threading.Thread(target=self._download_model_thread, args=(model,), daemon=True).start()

    def _download_model_thread(self, model):
        """Download model in a separate thread"""
        try:
            process = subprocess.Popen(['ollama', 'pull', model],
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      text=True,
                                      encoding='utf-8')

            try:
                _, stderr = process.communicate()

                if process.returncode == 0:
                    self.root.after(0, lambda: messagebox.showinfo("Download Complete",
                                                                 f"Model '{model}' downloaded successfully!"))
                    self.root.after(0, self.refresh_models)
                else:
                    # Clean the error message to remove non-printable characters
                    clean_stderr = ''.join(c for c in stderr if c.isprintable() or c in '\n\r\t')
                    self.root.after(0, lambda: messagebox.showerror("Download Failed",
                                                                  f"Failed to download model: {clean_stderr}"))
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Download Error",
                                                              f"Error during download: {str(e)}"))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Download error: {str(e)}"))

        finally:
            # Re-enable buttons
            self.root.after(0, lambda: self.start_stop_button.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.status_var.set("Status: Model not running"))

    def send_message(self, _):
        """Send a message to the model"""
        # Don't process if it's the default placeholder text
        if self.entry_is_default:
            return "break"

        message = self.user_input.get("1.0", tk.END).strip()
        if not message:
            return "break"

        if not self.model_running:
            # Start the model automatically and send the message after it's started
            self.append_to_chat("System", "Starting model automatically...")
            # Store the message to send after model starts
            self._pending_user_message = message
            # Clear input
            self.user_input.delete("1.0", tk.END)
            # Start the model
            self.start_model()
            return "break"

        # Clear input
        self.user_input.delete("1.0", tk.END)

        # Display user message
        self.append_to_chat("You", message)

        # Get response from model
        threading.Thread(target=self._get_model_response, args=(message,), daemon=True).start()

        return "break"  # Prevent default behavior (newline)

    def _get_model_response(self, message):
        """Get response from the model via API"""
        try:
            # Define the API endpoint
            url = "http://localhost:11434/api/generate"

            # Define the request payload
            payload = {
                "model": self.model_name.get(),
                "prompt": message,
                "stream": False
            }

            # Send the request
            response = requests.post(url, json=payload)

            # Check if the request was successful
            if response.status_code == 200:
                # Parse the response
                result = response.json()

                # Display the response
                self.root.after(0, lambda: self.append_to_chat("AI", result["response"]))
            else:
                self.root.after(0, lambda: self.append_to_chat("Error",
                                                            f"API error: {response.status_code} - {response.text}"))

        except requests.exceptions.ConnectionError:
            self.root.after(0, lambda: self.append_to_chat("Error",
                                                        "Could not connect to Ollama API. Make sure the model is running."))

        except Exception as e:
            self.root.after(0, lambda: self.append_to_chat("Error", f"Error: {str(e)}"))

    def _on_entry_click(self, _):
        """Function to handle click on the input field"""
        # Clear placeholder text
        if self.entry_is_default:
            self.user_input.delete("1.0", tk.END)
            self.user_input.config(foreground="black")
            self.entry_is_default = False

    def _on_focus_out(self, _):
        """Function to handle focus out of the input field"""
        if self.user_input.get("1.0", tk.END).strip() == "":
            self.user_input.delete("1.0", tk.END)
            self.user_input.insert(tk.END, "Type your message here and press Enter to send...")
            self.user_input.config(foreground="grey")
            self.entry_is_default = True

    def append_to_chat(self, sender, message):
        """Append a message to the chat history"""
        # Check if chat_history exists yet
        if not hasattr(self, 'chat_history') or not self.chat_history:
            # Store message to display later
            if not hasattr(self, 'pending_messages'):
                self.pending_messages = []

            import datetime
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            self.pending_messages.append((timestamp, sender, message))
            return

        self.chat_history.config(state=tk.NORMAL)

        # Add timestamp
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        # Format message
        formatted_message = f"[{timestamp}] {sender}:\n{message}\n\n"

        # Insert at end
        self.chat_history.insert(tk.END, formatted_message)

        # Auto-scroll to bottom
        self.chat_history.see(tk.END)

        # Disable editing
        self.chat_history.config(state=tk.DISABLED)

if __name__ == "__main__":
    root = tk.Tk()
    app = DeepSeekR1GUI(root)
    root.mainloop()
