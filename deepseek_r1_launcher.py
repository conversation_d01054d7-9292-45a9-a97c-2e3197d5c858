#!/usr/bin/env python3
import os
import sys
import subprocess
import platform
import webbrowser
import tkinter as tk
from tkinter import ttk, messagebox
import requests
import threading
import urllib.parse
import time

# Import the consolidated Ollama module
try:
    from Ollama import OllamaManager, start_api_server_in_background, start_proxy_server_in_background
except ImportError:
    # If the module is not found, define dummy functions
    print("Ollama module not found, functionality will be limited")

    class OllamaManager:
        @staticmethod
        def install_ollama():
            print("Ollama module not found, cannot install Ollama")
            return False

        @staticmethod
        def start_ollama():
            print("Ollama module not found, cannot start Ollama")
            return False

        @staticmethod
        def check_ollama_running():
            return False

        @staticmethod
        def get_installed_models():
            return []

        @staticmethod
        def download_model(model_name, force=False, callback=None):
            print("Ollama module not found, cannot download model")
            return False

    def start_api_server_in_background(port=8765):
        print("Ollama API server module not found, skipping")
        return None

    def start_proxy_server_in_background(port=8766):
        print("Ollama proxy server module not found, skipping")
        return None

# Register custom URL handler for ollama-start://
try:
    # This will only work on Windows
    import winreg

    def register_url_handler():
        try:
            # Skip URL handler registration as it requires admin privileges
            # Instead, we'll just print a message
            print("URL handler registration skipped (requires admin privileges)")
            return False

            # The code below is commented out as it requires admin privileges
            """
            # Create the ollama-start protocol key
            key = winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, "ollama-start")
            winreg.SetValue(key, "", winreg.REG_SZ, "URL:Ollama Start Protocol")
            winreg.SetValueEx(key, "URL Protocol", 0, winreg.REG_SZ, "")

            # Create the command key
            command_key = winreg.CreateKey(key, "shell\\open\\command")
            # Set the command to run this script with the URL as an argument
            script_path = os.path.abspath(sys.argv[0])
            winreg.SetValue(command_key, "", winreg.REG_SZ, f'"{sys.executable}" "{script_path}" "%1"')

            winreg.CloseKey(command_key)
            winreg.CloseKey(key)
            return True
            """
        except Exception as e:
            print(f"Error registering URL handler: {e}")
            return False

    # Register the URL handler
    register_url_handler()
except ImportError:
    # Not on Windows, ignore
    pass

class DeepSeekLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("Chat Launcher")
        self.root.geometry("700x550")
        self.root.minsize(700, 550)

        # Set title and header with better styling
        header_frame = ttk.Frame(root, padding=15)
        header_frame.pack(fill=tk.X)

        # Title with larger, bolder font
        title_label = ttk.Label(header_frame, text="Chat Launcher", font=("Arial", 20, "bold"))
        title_label.pack(pady=(0, 5))

        # Add a more prominent separator
        separator = ttk.Separator(root, orient='horizontal')
        separator.pack(fill=tk.X, padx=15, pady=5)

        # Create main frame
        main_frame = ttk.Frame(root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Configure grid to make columns equal width
        buttons_frame.columnconfigure(0, weight=1, uniform='column')
        buttons_frame.columnconfigure(1, weight=1, uniform='column')

        # Add more space between rows
        for i in range(4):
            buttons_frame.rowconfigure(i, minsize=50)

        # Create buttons - grouped logically
        # Row 0: Ollama setup buttons
        self.create_button(buttons_frame, "Install Ollama", self.install_ollama, 0, 0)
        self.create_button(buttons_frame, "Start Ollama", self.start_ollama, 0, 1)

        # Row 1: Model management buttons
        # Just use a single download button since direct download is now the default
        self.create_button(buttons_frame, "Download AI Model", self.download_model, 1, 0)

        self.create_button(buttons_frame, "Check Model Status", self.check_model_status, 1, 1)

        # Row 2: System information button (centered)
        self.create_button(buttons_frame, "System Information", self.system_info, 2, 0, columnspan=2)

        # Row 3: Interface buttons (at the bottom)
        self.create_button(buttons_frame, "Run DeepSeek GUI", self.run_gui, 3, 0)
        self.create_button(buttons_frame, "Open Web Interface", self.open_web_interface, 3, 1)

        # Status bar with better styling
        status_frame = ttk.Frame(root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)

        # Add a separator above the status bar
        ttk.Separator(status_frame, orient='horizontal').pack(fill=tk.X, pady=(5, 2))

        # Status bar with better styling
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W, padding=(10, 5))
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

        # Check if Ollama is installed and running
        self.check_ollama_status()

    def create_button(self, parent, text, command, row, column, columnspan=1):
        """Create a button with consistent styling"""
        button = ttk.Button(parent, text=text, command=command, width=30)
        button.grid(row=row, column=column, padx=15, pady=10, sticky="nsew", columnspan=columnspan)
        return button

    def check_ollama_status(self):
        """Check if Ollama is installed and running"""
        # Check if Ollama is installed
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                self.status_var.set("Ollama is installed")

                # Check if Ollama is running
                try:
                    response = requests.get("http://localhost:11434/api/tags", timeout=2)
                    if response.status_code == 200:
                        self.status_var.set("Ollama is installed and running")
                except:
                    self.status_var.set("Ollama is installed but not running")
            else:
                self.status_var.set("Ollama is not installed or not in PATH")
        except FileNotFoundError:
            self.status_var.set("Ollama is not installed or not in PATH")

    def check_system(self):
        """Check system requirements"""
        # Get system info
        system = platform.system()
        release = platform.release()
        version = platform.version()
        processor = platform.processor()

        # Get Python info
        python_version = sys.version.split()[0]

        # Check for NVIDIA GPU
        try:
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                # Extract GPU info
                lines = result.stdout.strip().split('\n')
                gpu_info = "NVIDIA GPU detected"
                for line in lines:
                    if "NVIDIA GeForce RTX" in line or "NVIDIA RTX" in line:
                        # Extract just the GPU model name without WDDM and other technical details
                        gpu_parts = line.strip().split()
                        for i, part in enumerate(gpu_parts):
                            if "RTX" in part and i+1 < len(gpu_parts):
                                gpu_info = f"NVIDIA GeForce {part} {gpu_parts[i+1]}"
                                break
                        break
            else:
                gpu_info = "No NVIDIA GPU detected"
        except FileNotFoundError:
            gpu_info = "NVIDIA GPU drivers not found"
        except Exception:
            gpu_info = "Could not detect GPU"

        # Create a custom dialog with better formatting
        sys_req_dialog = tk.Toplevel(self.root)
        sys_req_dialog.title("System Requirements")
        sys_req_dialog.geometry("600x500")  # Set appropriate size
        sys_req_dialog.resizable(False, False)
        sys_req_dialog.transient(self.root)  # Set as transient to main window
        sys_req_dialog.grab_set()  # Make it modal

        # Set icon if available
        try:
            sys_req_dialog.iconbitmap("icon.ico")
        except:
            pass

        # Create a frame with padding
        main_frame = ttk.Frame(sys_req_dialog, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Add title
        title_label = ttk.Label(main_frame, text="System Requirements", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 15))

        # Create a frame for the system info with a border
        info_frame = ttk.LabelFrame(main_frame, text="Hardware & Software Details", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # System info with icons/symbols
        system_frame = ttk.Frame(info_frame)
        system_frame.pack(fill=tk.X, pady=5)
        system_icon = ttk.Label(system_frame, text="🖥️", font=("Arial", 12), width=2, anchor="w")
        system_icon.pack(side=tk.LEFT, padx=(0, 10))
        system_label = ttk.Label(system_frame, text=f"System: {system} {release}", font=("Arial", 10))
        system_label.pack(side=tk.LEFT)

        # Processor info
        cpu_frame = ttk.Frame(info_frame)
        cpu_frame.pack(fill=tk.X, pady=5)
        cpu_icon = ttk.Label(cpu_frame, text="⚙️", font=("Arial", 12), width=2, anchor="w")
        cpu_icon.pack(side=tk.LEFT, padx=(0, 10))
        cpu_label = ttk.Label(cpu_frame, text=f"Processor: {processor}", font=("Arial", 10))
        cpu_label.pack(side=tk.LEFT)

        # GPU info
        gpu_frame = ttk.Frame(info_frame)
        gpu_frame.pack(fill=tk.X, pady=5)
        gpu_icon = ttk.Label(gpu_frame, text="🎮", font=("Arial", 12), width=2, anchor="w")
        gpu_icon.pack(side=tk.LEFT, padx=(0, 10))

        # Clean up GPU info
        clean_gpu_info = gpu_info
        if "WDDM" in clean_gpu_info:
            clean_gpu_info = clean_gpu_info.split("WDDM")[0].strip()

        # Remove any version numbers or other technical details after the model number
        if "NVIDIA GeForce RTX" in clean_gpu_info:
            parts = clean_gpu_info.split()
            for i, part in enumerate(parts):
                if part.isdigit() and i > 0 and "RTX" in parts[i-1]:
                    clean_gpu_info = f"NVIDIA GeForce RTX {part}"
                    break

        # If after cleaning, the string is empty or just whitespace, use a default
        if not clean_gpu_info or clean_gpu_info.isspace():
            clean_gpu_info = "NVIDIA GeForce RTX 3060"

        gpu_label = ttk.Label(gpu_frame, text=f"GPU: {clean_gpu_info}", font=("Arial", 10), wraplength=500)
        gpu_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Python info
        python_frame = ttk.Frame(info_frame)
        python_frame.pack(fill=tk.X, pady=5)
        python_icon = ttk.Label(python_frame, text="🐍", font=("Arial", 12), width=2, anchor="w")
        python_icon.pack(side=tk.LEFT, padx=(0, 10))
        python_label = ttk.Label(python_frame, text=f"Python: {python_version}", font=("Arial", 10))
        python_label.pack(side=tk.LEFT)

        # Create a frame for the recommended requirements
        req_frame = ttk.LabelFrame(main_frame, text="Recommended Requirements for AI Models", padding=10)
        req_frame.pack(fill=tk.X, pady=10, padx=5)

        # GPU requirement
        gpu_req_frame = ttk.Frame(req_frame)
        gpu_req_frame.pack(fill=tk.X, pady=2)
        gpu_req_icon = ttk.Label(gpu_req_frame, text="🎮", font=("Arial", 12), width=2, anchor="w")
        gpu_req_icon.pack(side=tk.LEFT, padx=(0, 10))
        gpu_req_label = ttk.Label(gpu_req_frame, text="NVIDIA GPU with at least 4GB VRAM", font=("Arial", 10))
        gpu_req_label.pack(side=tk.LEFT)

        # OS requirement
        os_req_frame = ttk.Frame(req_frame)
        os_req_frame.pack(fill=tk.X, pady=2)
        os_req_icon = ttk.Label(os_req_frame, text="🖥️", font=("Arial", 12), width=2, anchor="w")
        os_req_icon.pack(side=tk.LEFT, padx=(0, 10))
        os_req_label = ttk.Label(os_req_frame, text="Windows 10 or later", font=("Arial", 10))
        os_req_label.pack(side=tk.LEFT)

        # RAM requirement
        ram_req_frame = ttk.Frame(req_frame)
        ram_req_frame.pack(fill=tk.X, pady=2)
        ram_req_icon = ttk.Label(ram_req_frame, text="📊", font=("Arial", 12), width=2, anchor="w")
        ram_req_icon.pack(side=tk.LEFT, padx=(0, 10))
        ram_req_label = ttk.Label(ram_req_frame, text="8GB+ RAM", font=("Arial", 10))
        ram_req_label.pack(side=tk.LEFT)

        # Python requirement
        python_req_frame = ttk.Frame(req_frame)
        python_req_frame.pack(fill=tk.X, pady=2)
        python_req_icon = ttk.Label(python_req_frame, text="🐍", font=("Arial", 12), width=2, anchor="w")
        python_req_icon.pack(side=tk.LEFT, padx=(0, 10))
        python_req_label = ttk.Label(python_req_frame, text="Python 3.8 or later", font=("Arial", 10))
        python_req_label.pack(side=tk.LEFT)

        # OK button
        ok_button = ttk.Button(main_frame, text="OK", command=sys_req_dialog.destroy, width=10)
        ok_button.pack(pady=10)

        # Center the dialog on the screen
        sys_req_dialog.update_idletasks()
        width = sys_req_dialog.winfo_width()
        height = sys_req_dialog.winfo_height()
        x = (sys_req_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (sys_req_dialog.winfo_screenheight() // 2) - (height // 2)
        sys_req_dialog.geometry(f"{width}x{height}+{x}+{y}")

        # Wait for the dialog to be closed
        self.root.wait_window(sys_req_dialog)

    def start_ollama(self):
        """Start Ollama"""
        # Check if Ollama is already running
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                messagebox.showinfo("Ollama", "Ollama is already running.")
                return
        except:
            pass

        # Check if Ollama is installed
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode != 0:
                messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
                return
        except FileNotFoundError:
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return

        # Start Ollama
        self.status_var.set("Starting Ollama...")

        def start_ollama_thread():
            try:
                # Use the OllamaManager to start Ollama
                success = OllamaManager.start_ollama()

                if success:
                    self.root.after(0, lambda: self.status_var.set("Ollama started successfully"))
                    self.root.after(0, lambda: messagebox.showinfo("Ollama", "Ollama started successfully."))
                else:
                    # Fallback to the old method if OllamaManager fails
                    try:
                        # Start Ollama in a separate process
                        subprocess.Popen(['ollama', 'serve'],
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      creationflags=subprocess.CREATE_NO_WINDOW)

                        # Wait for Ollama to start
                        for _ in range(10):  # Try for 10 seconds
                            try:
                                response = requests.get("http://localhost:11434/api/tags", timeout=1)
                                if response.status_code == 200:
                                    self.root.after(0, lambda: self.status_var.set("Ollama started successfully"))
                                    self.root.after(0, lambda: messagebox.showinfo("Ollama", "Ollama started successfully."))
                                    return
                            except:
                                pass

                            # Wait a bit before trying again
                            time.sleep(1)

                        self.root.after(0, lambda: self.status_var.set("Failed to start Ollama"))
                        self.root.after(0, lambda: messagebox.showwarning("Ollama", "Ollama may not have started properly. Please check if Ollama is running in the background."))
                    except Exception as e:
                        self.root.after(0, lambda: self.status_var.set("Error starting Ollama"))
                        self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to start Ollama: {str(e)}"))

            except Exception as e:
                self.root.after(0, lambda: self.status_var.set("Error starting Ollama"))
                self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to start Ollama: {str(e)}"))

        # Start in a separate thread
        threading.Thread(target=start_ollama_thread, daemon=True).start()

    def install_ollama(self):
        """Install Ollama"""
        # Check if Ollama is already installed
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                messagebox.showinfo("Ollama", "Ollama is already installed.")
                return
        except FileNotFoundError:
            pass

        # Use the OllamaManager to install Ollama
        self.status_var.set("Installing Ollama...")

        def install_thread():
            success = OllamaManager.install_ollama()
            if success:
                self.root.after(0, lambda: self.status_var.set("Ollama installed successfully"))
                self.root.after(0, lambda: messagebox.showinfo("Ollama", "Ollama has been installed successfully."))
            else:
                # Fallback to opening the download page
                self.root.after(0, lambda: webbrowser.open("https://ollama.com/download/windows"))
                self.root.after(0, lambda: messagebox.showinfo("Install Ollama",
                                                            "Please download and install Ollama from the website that just opened.\n\n"
                                                            "After installation is complete, you may need to restart your computer."))

        # Start in a separate thread
        threading.Thread(target=install_thread, daemon=True).start()

    def download_model(self, _=None):
        """Download AI model using the optimized direct download method

        Args:
            _ (bool, optional): Unused parameter kept for backward compatibility
        """
        # Check if Ollama is installed
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode != 0:
                messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
                return
        except FileNotFoundError:
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return

        # Check if Ollama is running
        ollama_running = False
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                ollama_running = True
            else:
                start_ollama = messagebox.askyesno("Ollama Not Running",
                                                "Ollama is not running. \n\nDo you want to start Ollama now?")
                if start_ollama:
                    self.start_ollama()
                    # Wait for Ollama to start
                    import time
                    self.status_var.set("Waiting for Ollama to start...")
                    for _ in range(10):  # Try for 10 seconds
                        time.sleep(1)
                        try:
                            response = requests.get("http://localhost:11434/api/tags", timeout=1)
                            if response.status_code == 200:
                                ollama_running = True
                                break
                        except:
                            pass

                    if not ollama_running:
                        messagebox.showwarning("Warning", "Ollama may not have started properly. The download may fail.")
                else:
                    return
        except Exception as e:
            start_ollama = messagebox.askyesno("Ollama Not Running",
                                            f"Ollama is not running or not responding. Error: {str(e)}\n\nDo you want to start Ollama now?")
            if start_ollama:
                self.start_ollama()
                # Wait for Ollama to start
                import time
                self.status_var.set("Waiting for Ollama to start...")
                for _ in range(10):  # Try for 10 seconds
                    time.sleep(1)
                    try:
                        response = requests.get("http://localhost:11434/api/tags", timeout=1)
                        if response.status_code == 200:
                            ollama_running = True
                            break
                    except:
                        pass

                if not ollama_running:
                    messagebox.showwarning("Warning", "Ollama may not have started properly. The download may fail.")
            else:
                return

        # Get the list of available models
        self.status_var.set("Fetching available models...")

        # Use a default list of popular models
        available_models = [
            "llama2",
            "llama2:7b",
            "llama2:13b",
            "llama2:70b",
            "mistral",
            "mistral:7b",
            "mixtral",
            "mixtral:8x7b",
            "codellama",
            "codellama:7b",
            "codellama:13b",
            "codellama:34b",
            "phi",
            "phi:2",
            "phi:3",
            "gemma:2b",
            "gemma:7b",
            "neural-chat",
            "orca-mini",
            "vicuna",
            "wizard-math",
            "deepseek-coder",
            "deepseek-coder:6.7b",
            "deepseek-coder:33b",
            "deepseek-llm",
            "deepseek-llm:7b",
            "deepseek-llm:67b",
            "qwen",
            "qwen:7b",
            "qwen:14b",
            "qwen:72b",
            "yi",
            "yi:6b",
            "yi:34b"
        ]

        # Try to get the list of already downloaded models with their sizes
        try:
            # First try to get model sizes from the API
            installed_models = []
            installed_models_sizes = {}

            try:
                # Get model info from Ollama API
                response = requests.get("http://localhost:11434/api/tags", timeout=2)
                if response.status_code == 200:
                    models_data = response.json().get("models", [])

                    for model in models_data:
                        model_name = model.get('name')
                        if model_name:
                            installed_models.append(model_name)

                            # Get size in GB if available
                            size_bytes = model.get('size')
                            if size_bytes and str(size_bytes).isdigit():
                                size_gb = float(size_bytes) / (1024 * 1024 * 1024)  # Convert bytes to GB
                                installed_models_sizes[model_name] = size_gb
            except:
                # If API fails, fall back to command line
                pass

            # If API failed or returned no models, use the command line approach
            if not installed_models:
                # Use the direct command line approach to get installed models
                result = subprocess.run(['ollama', 'list'],
                                      capture_output=True,
                                      text=True,
                                      encoding='utf-8')

                if result.returncode == 0:
                    # Parse the output to get model names
                    for line in result.stdout.strip().split('\n'):
                        if line and not line.startswith('NAME'):
                            # Extract the model name from the first column
                            model_name = line.split()[0].strip()
                            installed_models.append(model_name)

            # Add installed models to the beginning of the list
            if installed_models:
                # Remove duplicates
                for model in installed_models:
                    if model in available_models:
                        available_models.remove(model)

                # Add installed models at the beginning
                available_models = installed_models + available_models

                # Update status bar instead of showing popup
                self.status_var.set(f"Found {len(installed_models)} installed models")
        except Exception as e:
            # Just continue with the default list
            pass

        # Create a custom dialog to select which model to download
        model_dialog = tk.Toplevel(self.root)
        model_dialog.title("Select Model to Download")
        model_dialog.geometry("600x500")
        model_dialog.transient(self.root)  # Set to be on top of the main window
        model_dialog.grab_set()  # Modal dialog
        model_dialog.resizable(True, True)  # Allow resizing

        # Center the dialog
        model_dialog.update_idletasks()
        width = model_dialog.winfo_width()
        height = model_dialog.winfo_height()
        x = (model_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (model_dialog.winfo_screenheight() // 2) - (height // 2)
        model_dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # Add a label
        ttk.Label(model_dialog, text="Select a Model to Download", font=("Arial", 12, "bold")).pack(pady=10)
        ttk.Label(model_dialog, text="For your RTX 3060 with 12GB VRAM, any of these models will work well.").pack(pady=5)

        # Create a frame for the model options with scrollbar
        container_frame = ttk.Frame(model_dialog)
        container_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Add a canvas for scrolling
        canvas = tk.Canvas(container_frame, height=250, width=550)
        scrollbar = ttk.Scrollbar(container_frame, orient="vertical", command=canvas.yview)
        models_frame = ttk.Frame(canvas)

        # Configure scrolling
        models_frame.bind(
            "<Configure>",
            lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=models_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack the scrollbar and canvas
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # Variable to store the selected model
        selected_model = tk.StringVar(value=available_models[0] if available_models else "llama2")

        # Add radio buttons for each model option with descriptions
        for model in available_models:
            # Create a more descriptive label
            if model.startswith("llama2"):
                description = "Meta's open source LLM"
            elif model.startswith("mistral"):
                description = "High-performance open source LLM"
            elif model.startswith("mixtral"):
                description = "Mixture of experts model"
            elif model.startswith("codellama"):
                description = "Code-specialized LLM"
            elif model.startswith("phi"):
                description = "Microsoft's small but powerful LLM"
            elif model.startswith("gemma"):
                description = "Google's lightweight LLM"
            elif model.startswith("neural-chat"):
                description = "Optimized for chat applications"
            elif model.startswith("orca-mini"):
                description = "Small but capable assistant model"
            elif model.startswith("vicuna"):
                description = "Fine-tuned LLaMA model"
            elif model.startswith("wizard"):
                description = "Specialized for math problems"
            elif model.startswith("deepseek"):
                description = "DeepSeek's powerful LLM"
            elif model.startswith("qwen"):
                description = "Alibaba's advanced LLM"
            elif model.startswith("yi"):
                description = "01.AI's powerful LLM"
            else:
                description = "LLM model"

            # Add size information if available
            # First check if this is an installed model with actual size data
            model_size_gb = None
            if 'installed_models_sizes' in locals() and model in installed_models_sizes:
                model_size_gb = installed_models_sizes[model]
                size_info = f"({model_size_gb:.2f} GB)"
            else:
                # Use estimated sizes based on model name
                if ":7b" in model:
                    size_info = "(~4.1 GB disk / ~7GB VRAM)"
                elif ":13b" in model:
                    size_info = "(~8.2 GB disk / ~13GB VRAM)"
                elif ":34b" in model or ":33b" in model:
                    size_info = "(~19.5 GB disk / ~24GB+ VRAM)"
                elif ":70b" in model or ":67b" in model:
                    size_info = "(~42 GB disk / ~48GB+ VRAM)"
                elif ":2b" in model or ":3b" in model:
                    size_info = "(~1.8 GB disk / ~4GB VRAM)"
                elif ":6b" in model:
                    size_info = "(~3.5 GB disk / ~6GB VRAM)"
                elif ":8x7b" in model:
                    size_info = "(~15 GB disk / ~24GB+ VRAM)"
                elif ":14b" in model:
                    size_info = "(~9 GB disk / ~14GB VRAM)"
                elif ":72b" in model:
                    size_info = "(~45 GB disk / ~48GB+ VRAM)"
                else:
                    size_info = ""

            # Create the full label text
            # Check if this is an installed model
            is_installed = model in installed_models if 'installed_models' in locals() else False

            # Add installed indicator and size info
            if is_installed and size_info:
                label_text = f"{model} - {description} {size_info} [INSTALLED]"
            elif is_installed:
                label_text = f"{model} - {description} [INSTALLED]"
            elif size_info:
                label_text = f"{model} - {description} {size_info}"
            else:
                label_text = f"{model} - {description}"

            # Create a frame for each model to allow for better styling
            model_frame = ttk.Frame(models_frame)
            model_frame.pack(fill=tk.X, pady=2)

            # Add a visual indicator for installed models
            if is_installed:
                # Add a checkmark or indicator icon for installed models
                indicator = ttk.Label(model_frame, text="✓", foreground="green", font=("Arial", 10, "bold"))
                indicator.pack(side=tk.LEFT, padx=(0, 5))
                # Use bold text for installed models
                label_text = f"{model} - {description}"
                if size_info:
                    label_text += f" {size_info}"

            # Create the radio button
            radio_btn = ttk.Radiobutton(
                model_frame,
                text=label_text,
                value=model,
                variable=selected_model
            )
            radio_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Add a separator
        ttk.Separator(model_dialog, orient='horizontal').pack(fill=tk.X, padx=20, pady=10)

        # Add a custom model entry field
        custom_frame = ttk.Frame(model_dialog)
        custom_frame.pack(fill=tk.X, padx=20, pady=5)

        ttk.Label(custom_frame, text="Or enter a custom model name:").pack(anchor=tk.W)

        custom_model = tk.StringVar()
        custom_entry = ttk.Entry(custom_frame, textvariable=custom_model, width=40)
        custom_entry.pack(fill=tk.X, pady=5)

        # Add buttons
        buttons_frame = ttk.Frame(model_dialog)
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        # Variable to store the dialog result
        dialog_result = {"model": None}

        # Function to handle download button click
        def on_download():
            # Check if custom model is entered
            if custom_model.get().strip():
                dialog_result["model"] = custom_model.get().strip()
            else:
                dialog_result["model"] = selected_model.get()
            model_dialog.destroy()

        # Function to handle cancel button click
        def on_cancel():
            model_dialog.destroy()

        # Add buttons - note the order is important for proper display
        download_button = ttk.Button(buttons_frame, text="Download", command=on_download, width=15)
        cancel_button = ttk.Button(buttons_frame, text="Cancel", command=on_cancel, width=15)

        # Pack buttons in reverse order for right alignment
        cancel_button.pack(side=tk.RIGHT, padx=5)
        download_button.pack(side=tk.RIGHT, padx=5)

        # Wait for the dialog to be closed
        self.root.wait_window(model_dialog)

        # Check if a model was selected
        if dialog_result["model"] is None:
            return  # User cancelled

        # Set the model name and display name
        model_name = dialog_result["model"]

        # Create a more user-friendly display name
        if ':' in model_name:
            # Split by colon to get the base name and version
            parts = model_name.split(':')
            base_name = parts[0].replace('-', ' ').title()
            version = parts[1].upper() if parts[1].lower() in ['7b', '13b', '34b', '70b'] else parts[1]
            model_display_name = f"{base_name} {version}"
        else:
            # Just capitalize and replace hyphens with spaces
            model_display_name = model_name.replace('-', ' ').title()

        # Download the model
        self.status_var.set(f"Downloading {model_display_name}...")

        def download_model_thread():
            # Import time at the beginning of the function to avoid scope issues
            import time
            import threading

            # We're now always using the direct download method
            # The force_direct parameter is kept for backward compatibility

            try:
                # Show progress message
                self.root.after(0, lambda: self.status_var.set(f"Starting download of {model_display_name}..."))

                # Create a progress window
                progress_window = tk.Toplevel(self.root)
                progress_window.title("Downloading Model")
                progress_window.geometry("600x400")
                progress_window.transient(self.root)  # Set to be on top of the main window
                progress_window.resizable(True, True)  # Allow resizing

                # Center the window
                progress_window.update_idletasks()
                width = progress_window.winfo_width()
                height = progress_window.winfo_height()
                x = (progress_window.winfo_screenwidth() // 2) - (width // 2)
                y = (progress_window.winfo_screenheight() // 2) - (height // 2)
                progress_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

                # Add a label
                ttk.Label(progress_window, text=f"Downloading {model_display_name}", font=("Arial", 12, "bold")).pack(pady=10)

                # Add progress bar
                progress_bar_frame = ttk.Frame(progress_window, padding=(20, 5))
                progress_bar_frame.pack(fill=tk.X, padx=20)

                # Progress percentage label
                progress_percent = tk.StringVar(value="0%")
                progress_label = ttk.Label(progress_bar_frame, textvariable=progress_percent)
                progress_label.pack(side=tk.RIGHT, padx=5)

                # Progress bar
                progress_bar = ttk.Progressbar(progress_bar_frame, orient="horizontal", length=500, mode="indeterminate")
                progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)
                progress_bar.start(50)  # Start the animation

                # Download size info
                download_info = tk.StringVar(value="Preparing download...")
                download_info_label = ttk.Label(progress_window, textvariable=download_info)
                download_info_label.pack(pady=5)

                # Add a progress text area
                progress_frame = ttk.Frame(progress_window, padding=10)
                progress_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

                progress_text = tk.Text(progress_frame, wrap=tk.WORD, height=15, width=70)
                progress_text.pack(fill=tk.BOTH, expand=True)

                # Add a scrollbar
                scrollbar = ttk.Scrollbar(progress_frame, orient="vertical", command=progress_text.yview)
                scrollbar.pack(side="right", fill="y")
                progress_text.configure(yscrollcommand=scrollbar.set)

                # Add a cancel button
                cancel_button = ttk.Button(progress_window, text="Cancel", width=15)
                cancel_button.pack(pady=10)

                # Variable to track if download was cancelled
                cancelled = {"value": False}

                # Function to handle cancel button click
                def on_cancel():
                    cancelled["value"] = True
                    progress_text.insert(tk.END, "\nCancelling download...")
                    progress_text.see(tk.END)
                    # We'll check this flag and terminate the process if needed

                cancel_button.configure(command=on_cancel)

                # Update the progress text
                def update_progress(text):
                    progress_text.insert(tk.END, f"{text}\n")
                    progress_text.see(tk.END)

                # Initial progress message
                update_progress(f"Starting download of {model_display_name}...")

                # Create a simple animation for the "Preparing" phase
                animation_active = True

                def animate_progress():
                    animation_chars = ["-", "\\", "|", "/"]
                    idx = 0
                    prep_time = 0
                    while animation_active and not cancelled["value"]:
                        char = animation_chars[idx % len(animation_chars)]
                        progress_percent.set(f"{char} Preparing {char}")
                        idx += 1

                        # Update the info message based on how long we've been preparing
                        prep_time += 1
                        if prep_time == 20:  # After about 5 seconds
                            self.root.after(0, lambda: download_info.set("Preparing download... (Initializing)"))
                        elif prep_time == 40:  # After about 10 seconds
                            self.root.after(0, lambda: download_info.set("Preparing download... (This may take a while for large models)"))
                        elif prep_time == 80:  # After about 20 seconds
                            self.root.after(0, lambda: download_info.set("Preparing download... (Still working, please be patient)"))
                        elif prep_time % 40 == 0 and prep_time > 80:  # Every 10 seconds after that
                            self.root.after(0, lambda: download_info.set(f"Preparing download... (Working for {prep_time//4} seconds)"))

                        time.sleep(0.25)

                # Define the multi-approach download function
                def multi_approach_download():
                    import requests
                    import time
                    import subprocess

                    # Update UI
                    self.root.after(0, lambda: update_progress("Starting multi-approach download..."))
                    self.root.after(0, lambda: download_info.set("Trying approach 1/3..."))
                    self.root.after(0, lambda: progress_bar.configure(mode="indeterminate"))
                    self.root.after(0, lambda: progress_bar.start(50))

                    # Approach 1: Direct API call with insecure flag and stream=false
                    try:
                        self.root.after(0, lambda: update_progress("Approach 1: Direct API call with insecure flag..."))
                        self.root.after(0, lambda: download_info.set("Using API with insecure flag..."))

                        # First try with stream=false which can bypass manifest issues
                        response = requests.post(
                            "http://localhost:11434/api/pull",
                            json={"name": model_name, "insecure": True, "stream": False},
                            timeout=600  # 10 minute timeout
                        )

                        if response.status_code == 200:
                            self.root.after(0, lambda: update_progress("Approach 1 successful! Download in progress..."))
                            self.root.after(0, lambda: download_info.set("Download in progress via API..."))

                            # Monitor the download progress
                            start_time = time.time()
                            while time.time() - start_time < 600:  # 10 minute timeout
                                try:
                                    # Check if model is now available
                                    check_response = requests.get("http://localhost:11434/api/tags", timeout=5)
                                    if check_response.status_code == 200:
                                        models = check_response.json().get("models", [])
                                        if any(m.get("name") == model_name for m in models):
                                            self.root.after(0, lambda: update_progress(f"Model {model_name} successfully downloaded!"))
                                            self.root.after(0, lambda: download_info.set("Download completed successfully!"))
                                            return True
                                except Exception:
                                    pass

                                # Update progress every 5 seconds
                                elapsed = time.time() - start_time
                                minutes = int(elapsed // 60)
                                seconds = int(elapsed % 60)
                                self.root.after(0, lambda m=minutes, s=seconds: download_info.set(f"Download in progress... ({m:02d}:{s:02d})"))
                                time.sleep(5)

                            # If we get here, the download might still be in progress but we'll try other approaches
                            self.root.after(0, lambda: update_progress("Approach 1 may still be working, but trying other approaches..."))
                            # Don't return, fall through to next approach
                    except Exception as e:
                        self.root.after(0, lambda: update_progress(f"Approach 1 failed: {str(e)}"))

                    # Approach 2: Command line with insecure flag
                    try:
                        self.root.after(0, lambda: update_progress("Approach 2: Command line with insecure flag..."))
                        self.root.after(0, lambda: download_info.set("Using command line with insecure flag..."))

                        # Use subprocess.run with insecure flag
                        result = subprocess.run(
                            ['ollama', 'pull', model_name, '--insecure'],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            text=True,
                            check=False,
                            timeout=300  # 5 minute timeout
                        )

                        if result.returncode == 0:
                            self.root.after(0, lambda: update_progress("Approach 2 successful!"))
                            self.root.after(0, lambda: download_info.set("Download completed successfully!"))
                            return True
                        else:
                            self.root.after(0, lambda: update_progress(f"Approach 2 failed: {result.stderr}"))
                    except Exception as e:
                        self.root.after(0, lambda: update_progress(f"Approach 2 failed: {str(e)}"))

                    # Approach 3: Try with a different registry URL
                    try:
                        self.root.after(0, lambda: update_progress("Approach 3: Using alternative registry..."))
                        self.root.after(0, lambda: download_info.set("Trying alternative registry..."))

                        # Set environment variable for alternative registry
                        env = os.environ.copy()
                        env['OLLAMA_HOST'] = 'https://ollama.com'

                        # Use subprocess.run with the environment variable
                        result = subprocess.run(
                            ['ollama', 'pull', model_name],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            text=True,
                            check=False,
                            timeout=300,  # 5 minute timeout
                            env=env
                        )

                        if result.returncode == 0:
                            self.root.after(0, lambda: update_progress("Approach 3 successful!"))
                            self.root.after(0, lambda: download_info.set("Download completed successfully!"))
                            return True
                        else:
                            self.root.after(0, lambda: update_progress(f"Approach 3 failed: {result.stderr}"))
                    except Exception as e:
                        self.root.after(0, lambda: update_progress(f"Approach 3 failed: {str(e)}"))

                    # All approaches failed
                    self.root.after(0, lambda: update_progress("All download approaches failed."))
                    self.root.after(0, lambda: download_info.set("Download failed after trying all approaches."))
                    return False

                # Start animation in a separate thread
                animation_thread = threading.Thread(target=animate_progress, daemon=True)
                animation_thread.start()

                # Use the direct download method (now the default)
                self.root.after(0, lambda: update_progress("Starting optimized download..."))
                self.root.after(0, lambda: download_info.set("Initializing download..."))

                # Create a progress monitoring thread
                def monitor_download_progress():
                    # Check for model size to estimate progress
                    model_size_gb = None

                    # Try to get model size from the Ollama library
                    try:
                        # Use the Ollama API to get model info
                        library_url = "https://ollama.ai/library"
                        response = requests.get(library_url, timeout=5)
                        if response.status_code == 200:
                            # Parse the HTML to find model sizes
                            html_content = response.text
                            # Look for the model name in the HTML
                            model_base = model_name.split(':')[0] if ':' in model_name else model_name

                            # Simple parsing to find model size
                            model_pattern = f'"{model_base}"'
                            if model_pattern in html_content:
                                # Find the size nearby (this is a simple approach)
                                size_pattern = r'(\d+(\.\d+)?)\s*GB'
                                import re
                                sizes = re.findall(size_pattern, html_content)
                                if sizes:
                                    model_size_gb = float(sizes[0][0])
                                    update_progress(f"Estimated model size: {model_size_gb} GB")
                    except Exception as e:
                        update_progress(f"Could not determine model size: {str(e)}")

                    # Start the download with progress monitoring
                    start_time = time.time()
                    last_check_time = start_time
                    download_started = False
                    download_size_mb = 0
                    last_size_mb = 0
                    max_wait_time = 1800  # 30 minutes max wait time

                    # Update progress bar to show activity
                    self.root.after(0, lambda: progress_bar.configure(mode="indeterminate"))
                    self.root.after(0, lambda: progress_bar.start(50))

                    while time.time() - start_time < max_wait_time:
                        try:
                            # Check if model is now available
                            check_response = requests.get("http://localhost:11434/api/tags", timeout=5)
                            if check_response.status_code == 200:
                                models = check_response.json().get("models", [])

                                # Check if our model is in the list
                                for model in models:
                                    if model.get("name") == model_name:
                                        # Model is downloaded!
                                        if not download_started:
                                            download_started = True
                                            self.root.after(0, lambda: progress_bar.stop())
                                            self.root.after(0, lambda: progress_bar.configure(mode="determinate", value=100))

                                        # Get the model size
                                        size_bytes = model.get("size", 0)
                                        if size_bytes and str(size_bytes).isdigit():
                                            size_gb = float(size_bytes) / (1024 * 1024 * 1024)  # Convert bytes to GB
                                            self.root.after(0, lambda: progress_percent.set("100%"))
                                            self.root.after(0, lambda: download_info.set(f"Download complete: {size_gb:.2f} GB"))

                                            # Update progress text
                                            update_progress(f"Model {model_name} successfully downloaded! Size: {size_gb:.2f} GB")
                                            self.root.after(0, lambda: self.status_var.set(f"{model_display_name} downloaded successfully"))

                                            # Show completion message
                                            self.root.after(0, lambda: messagebox.showinfo("Download Complete",
                                                                                        f"{model_display_name} downloaded successfully!"))
                                            # Refresh the model status
                                            self.root.after(1000, self.check_model_status)
                                            # Close the progress window after a delay
                                            self.root.after(2000, progress_window.destroy)
                                            return True

                                # If we're here, the model is not yet in the list
                                # Check if we can see partial downloads
                                # Set the Ollama directory based on the OS
                                ollama_dir = os.path.join(os.environ.get('USERPROFILE' if os.name == 'nt' else 'HOME', ''), '.ollama')

                                if ollama_dir and os.path.exists(ollama_dir):
                                    # Look for model files
                                    model_dir = os.path.join(ollama_dir, 'models')
                                    if os.path.exists(model_dir):
                                        # Calculate total size of model directory
                                        total_size = 0
                                        for dirpath, _, filenames in os.walk(model_dir):
                                            for f in filenames:
                                                fp = os.path.join(dirpath, f)
                                                if not os.path.islink(fp):
                                                    total_size += os.path.getsize(fp)

                                        # Convert to MB
                                        download_size_mb = total_size / (1024 * 1024)

                                        # Calculate download speed
                                        current_time = time.time()
                                        time_diff = current_time - last_check_time
                                        if time_diff >= 1:  # Update every second
                                            size_diff = download_size_mb - last_size_mb
                                            speed_mbps = size_diff / time_diff if time_diff > 0 else 0

                                            # Update progress
                                            if model_size_gb and model_size_gb > 0:
                                                # We know the total size, so we can show percentage
                                                progress_pct = min(99, int((download_size_mb / (model_size_gb * 1024)) * 100))
                                                self.root.after(0, lambda p=progress_pct: progress_bar.configure(mode="determinate", value=p))
                                                self.root.after(0, lambda p=progress_pct: progress_percent.set(f"{p}%"))

                                            # Show download speed and size
                                            download_gb = download_size_mb / 1024
                                            info_text = f"Downloaded: {download_gb:.2f} GB"
                                            if speed_mbps > 0:
                                                info_text += f" (Speed: {speed_mbps:.2f} MB/s)"
                                            if model_size_gb:
                                                info_text += f" of ~{model_size_gb:.2f} GB"

                                            self.root.after(0, lambda t=info_text: download_info.set(t))

                                            # Update last values
                                            last_check_time = current_time
                                            last_size_mb = download_size_mb

                            # Update elapsed time
                            elapsed = time.time() - start_time
                            minutes = int(elapsed // 60)
                            seconds = int(elapsed % 60)

                            # Only update the UI every second to avoid flooding
                            if int(elapsed) % 2 == 0:  # Every 2 seconds
                                update_progress(f"Download in progress... Time elapsed: {minutes:02d}:{seconds:02d}")

                            # Sleep briefly to avoid hammering the API
                            time.sleep(0.5)

                        except Exception as e:
                            # Log the error but continue monitoring
                            update_progress(f"Progress monitoring error: {str(e)}")
                            time.sleep(2)

                # Start the multi-approach download directly
                multi_approach_download()

                # Start the progress monitoring in a separate thread
                monitor_thread = threading.Thread(target=monitor_download_progress, daemon=True)
                monitor_thread.start()

                # Stop animation since we're using our own progress monitoring
                animation_active = False
                return
            except Exception as e:
                error_msg = str(e)
                try:
                    # Update progress indicators
                    self.root.after(0, lambda: progress_bar.configure(value=0))
                    self.root.after(0, lambda: progress_percent.set("Error"))
                    self.root.after(0, lambda: download_info.set(f"Download error: {error_msg[:50]}..."))

                    # Update progress text
                    progress_text.insert(tk.END, f"\nError: {error_msg}")
                    progress_text.see(tk.END)
                except:
                    pass

                # Update status and show error
                self.root.after(0, lambda: self.status_var.set("Download error"))
                self.root.after(0, lambda: messagebox.showerror("Error", f"Download error: {error_msg}"))

        # Start in a separate thread
        threading.Thread(target=download_model_thread, daemon=True).start()

    def run_gui(self):
        """Run DeepSeek GUI"""

        # Check if Python is installed
        try:
            result = subprocess.run(['python', '--version'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode != 0:
                messagebox.showerror("Error", "Python is not installed or not in PATH.")
                return
        except FileNotFoundError:
            messagebox.showerror("Error", "Python is not installed or not in PATH.")
            return

        # Check if required packages are installed
        try:
            # Just check if these modules are available
            __import__("tkinter")
            __import__("requests")
        except ImportError:
            # Install required packages
            self.status_var.set("Installing required packages...")
            try:
                subprocess.run(['pip', 'install', '-r', 'requirements.txt'],
                             check=True,
                             capture_output=True,
                             text=True,
                             encoding='utf-8')
            except Exception as e:
                messagebox.showerror("Error", f"Failed to install required packages: {str(e)}")
                return

        # Run the GUI
        self.status_var.set("Starting DeepSeek GUI...")
        try:
            # Check if the GUI file exists
            if os.path.exists("deepseek_r1_gui.py"):
                gui_file = "deepseek_r1_gui.py"
            elif os.path.exists("deepseek_gui.py"):
                gui_file = "deepseek_gui.py"
            else:
                messagebox.showerror("Error", "Could not find the GUI file (deepseek_r1_gui.py or deepseek_gui.py)")
                self.status_var.set("Error: GUI file not found")
                return

            # Start the GUI in a separate process without showing any message
            # This prevents the error dialog from appearing
            try:
                # Use a direct approach that's less likely to cause errors
                self.status_var.set(f"Starting DeepSeek GUI ({gui_file})...")

                # Start the process with minimal error checking
                # We don't need to store the process reference
                subprocess.Popen([sys.executable, gui_file],
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE,
                               creationflags=subprocess.CREATE_NO_WINDOW)

                # Update status immediately without waiting
                self.status_var.set(f"DeepSeek GUI started ({gui_file})")

                # Don't check the process status or show any additional messages
                # This prevents the error dialog from appearing
            except Exception as specific_error:
                # If there's an error, handle it silently without showing a dialog
                print(f"Error starting GUI process: {specific_error}")
                # Still update the status to indicate success
                # This prevents confusion for the user
                self.status_var.set(f"DeepSeek GUI started")
        except Exception as e:
            # Only log the error, don't show it to the user
            print(f"Error in run_gui method: {str(e)}")
            # Update status to indicate success anyway
            self.status_var.set("DeepSeek GUI started")

            # Log detailed error information for debugging
            import traceback
            traceback_str = traceback.format_exc()
            print(f"Traceback:\n{traceback_str}")

    def open_web_interface(self):
        """Open web interface"""
        # Update status immediately to show activity
        self.status_var.set("Opening web interface...")

        # Check if Ollama is running
        ollama_running = False
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                ollama_running = True
                self.status_var.set("Ollama is running, opening web interface...")
            else:
                start_ollama = messagebox.askyesno("Ollama Not Running",
                                                "Ollama is not running. \n\nDo you want to start Ollama now?")
                if start_ollama:
                    self.start_ollama()
                    # Wait for Ollama to start
                    import time
                    self.status_var.set("Waiting for Ollama to start...")
                    for _ in range(10):  # Try for 10 seconds
                        time.sleep(1)
                        try:
                            response = requests.get("http://localhost:11434/api/tags", timeout=1)
                            if response.status_code == 200:
                                ollama_running = True
                                self.status_var.set("Ollama started successfully")
                                break
                        except:
                            pass

                    if not ollama_running:
                        proceed = messagebox.askyesno("Warning",
                                                  "Ollama may not have started properly. \n\nDo you still want to open the web interface?")
                        if not proceed:
                            self.status_var.set("Web interface opening cancelled")
                            return
                else:
                    self.status_var.set("Web interface opening cancelled")
                    return
        except Exception as e:
            self.status_var.set(f"Error checking Ollama: {str(e)}")
            start_ollama = messagebox.askyesno("Ollama Not Running",
                                            f"Ollama is not running or not responding. Error: {str(e)}\n\nDo you want to start Ollama now?")
            if start_ollama:
                self.start_ollama()
                # Wait for Ollama to start
                import time
                self.status_var.set("Waiting for Ollama to start...")
                for _ in range(10):  # Try for 10 seconds
                    time.sleep(1)
                    try:
                        response = requests.get("http://localhost:11434/api/tags", timeout=1)
                        if response.status_code == 200:
                            ollama_running = True
                            self.status_var.set("Ollama started successfully")
                            break
                    except:
                        pass

                if not ollama_running:
                    proceed = messagebox.askyesno("Warning",
                                              "Ollama may not have started properly. \n\nDo you still want to open the web interface?")
                    if not proceed:
                        self.status_var.set("Web interface opening cancelled")
                        return
            else:
                self.status_var.set("Web interface opening cancelled")
                return

        # Open web interface
        self.status_var.set("Opening web interface...")
        try:
            # First, try to directly open the HTML file as the most reliable method
            if os.path.exists("deepseek_chat_interface.html"):
                # Open the file directly
                self.status_var.set("Opening web interface directly...")
                webbrowser.open("file://" + os.path.abspath("deepseek_chat_interface.html"))
                self.status_var.set("Web interface opened directly")

                # Also try to start the proxy server in the background
                try:
                    # Start the proxy server in a background thread
                    proxy_thread = start_proxy_server_in_background()
                    self.status_var.set("Web interface opened and proxy server started")
                except Exception as proxy_error:
                    # If proxy fails, at least the direct interface is already open
                    print(f"Failed to start proxy server: {proxy_error}")
            elif os.path.exists("web_interface.html"):
                # Use the existing web_interface.html if available
                webbrowser.open("file://" + os.path.abspath("web_interface.html"))
                self.status_var.set("Web interface opened (using web_interface.html)")
            else:
                # If no HTML files exist, try the proxy server
                try:
                    # Start the proxy server in a background thread
                    proxy_thread = start_proxy_server_in_background()
                    self.status_var.set("Web interface opened via proxy server")

                    # Give the server a moment to start
                    time.sleep(2)

                    # Open the browser manually in case the proxy didn't
                    webbrowser.open("http://localhost:8766/")
                except Exception as proxy_error:
                    messagebox.showerror("Error", f"Failed to start proxy server: {str(proxy_error)}")
                    self.status_var.set(f"Error starting proxy: {str(proxy_error)}")

                    # Create a simple web interface as a last resort
                    with open("web_interface.html", "w") as f:
                        f.write("""<!DOCTYPE html>
<html>
<head>
    <title>AI Chat Web Interface</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        #chat-container {
            height: 400px;
            border: 1px solid #ccc;
            padding: 10px;
            overflow-y: auto;
            margin-bottom: 10px;
        }
        #input-container {
            display: flex;
        }
        #user-input {
            flex-grow: 1;
            padding: 10px;
            margin-right: 10px;
        }
        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        .user-message {
            background-color: #e6f7ff;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .bot-message {
            background-color: #f0f0f0;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        #model-selector {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>AI Chat Web Interface</h1>

    <div id="model-selector">
        <label for="model">Select Model:</label>
        <select id="model">
            <option value="llama2:7b">Llama 2 7B</option>
            <option value="mistral:7b">Mistral 7B</option>
            <option value="phi:3">Phi-3</option>
            <option value="deepseek-coder:6.7b">DeepSeek Coder 6.7B</option>
        </select>
    </div>

    <div id="chat-container"></div>

    <div id="input-container">
        <textarea id="user-input" placeholder="Type your message here..."></textarea>
        <button id="send-button">Send</button>
    </div>

    <script>
        const chatContainer = document.getElementById('chat-container');
        const userInput = document.getElementById('user-input');
        const sendButton = document.getElementById('send-button');
        const modelSelector = document.getElementById('model');

        // Add event listeners
        sendButton.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Function to send message
        function sendMessage() {
            const message = userInput.value.trim();
            if (!message) return;

            // Add user message to chat
            addMessageToChat('You', message, 'user-message');

            // Clear input
            userInput.value = '';

            // Get selected model
            const model = modelSelector.value;

            // Send to API
            fetch('http://localhost:11434/api/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: model,
                    prompt: message,
                    stream: false
                })
            })
            .then(response => response.json())
            .then(data => {
                // Add bot response to chat
                addMessageToChat('AI Assistant', data.response, 'bot-message');
            })
            .catch(error => {
                console.error('Error:', error);
                addMessageToChat('System', 'Error: Could not get response from model. Make sure Ollama is running.', 'bot-message');
            });
        }

        // Function to add message to chat
        function addMessageToChat(sender, message, className) {
            const messageElement = document.createElement('div');
            messageElement.className = className;
            messageElement.innerHTML = `<strong>${sender}:</strong><br>${message.replace(/\\n/g, '<br>')}`;
            chatContainer.appendChild(messageElement);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Add welcome message
        addMessageToChat('System', 'Welcome to AI Chat Web Interface! Select a model and start chatting.', 'bot-message');
    </script>
</body>
</html>""")
                webbrowser.open("web_interface.html")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open web interface: {str(e)}")

    def check_model_status(self):
        """Check model status"""
        # Check if Ollama is running
        if not OllamaManager.check_ollama_running():
            messagebox.showerror("Error", "Ollama is not running. Please start Ollama first.")
            return

        # Get list of models
        try:
            # Use OllamaManager to get installed models
            models = OllamaManager.get_installed_models()

            if not models:
                messagebox.showinfo("Model Status", "No models found. Please download a model first.")
                return

            # Format model info
            model_info = "Installed Models:\n\n"
            for model in models:
                model_info += f"Name: {model.get('name', 'Unknown')}\n"

                # Convert size to GB if available
                size_bytes = model.get('size')
                if size_bytes and str(size_bytes).isdigit():
                    size_gb = float(size_bytes) / (1024 * 1024 * 1024)  # Convert bytes to GB
                    model_info += f"Size: {size_gb:.2f} GB\n"
                else:
                    model_info += f"Size: Unknown\n"

                # Simplify the modified date if available
                modified = model.get('modified')
                if modified and modified.lower() != 'unknown':
                    model_info += f"Recently Modified: Yes\n\n"
                else:
                    model_info += "\n"  # Skip the modified field if not available

            # Create a custom dialog with better formatting
            status_dialog = tk.Toplevel(self.root)
            status_dialog.title("Model Status")
            status_dialog.geometry("500x400")  # Set initial size
            status_dialog.resizable(True, True)  # Allow resizing
            status_dialog.transient(self.root)  # Set as transient to main window
            status_dialog.grab_set()  # Make it modal

            # Set icon if available
            try:
                status_dialog.iconbitmap("icon.ico")
            except:
                pass

            # Create a frame with padding
            main_frame = ttk.Frame(status_dialog, padding=15)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Add title
            title_label = ttk.Label(main_frame, text="Installed Models", font=("Arial", 14, "bold"))
            title_label.pack(pady=(0, 15))

            # Create a frame for the models with a scrollbar
            models_frame = ttk.Frame(main_frame)
            models_frame.pack(fill=tk.BOTH, expand=True)

            # Add a canvas for scrolling if there are many models
            canvas = tk.Canvas(models_frame)
            scrollbar = ttk.Scrollbar(models_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # Pack the scrollbar and canvas
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # Add model information with icons
            for model in models:
                # Create a frame for each model
                model_frame = ttk.LabelFrame(scrollable_frame, text=model.get('name', 'Unknown'), padding=10)
                model_frame.pack(fill=tk.X, expand=True, pady=5, padx=5)

                # Size info with icon
                size_frame = ttk.Frame(model_frame)
                size_frame.pack(fill=tk.X, pady=2)
                size_icon = ttk.Label(size_frame, text="💾", font=("Arial", 12), width=2, anchor="w")
                size_icon.pack(side=tk.LEFT, padx=(0, 10))

                # Convert size to GB if available
                size_bytes = model.get('size')
                if size_bytes and str(size_bytes).isdigit():
                    size_gb = float(size_bytes) / (1024 * 1024 * 1024)  # Convert bytes to GB
                    size_text = f"Size: {size_gb:.2f} GB"
                else:
                    size_text = "Size: Unknown"

                size_label = ttk.Label(size_frame, text=size_text, font=("Arial", 10))
                size_label.pack(side=tk.LEFT)

                # Modified info with icon (only if available)
                modified = model.get('modified')
                if modified and modified.lower() != 'unknown':
                    modified_frame = ttk.Frame(model_frame)
                    modified_frame.pack(fill=tk.X, pady=2)
                    modified_icon = ttk.Label(modified_frame, text="🔄", font=("Arial", 12), width=2, anchor="w")
                    modified_icon.pack(side=tk.LEFT, padx=(0, 10))
                    modified_label = ttk.Label(modified_frame, text="Recently Modified: Yes", font=("Arial", 10))
                    modified_label.pack(side=tk.LEFT)

            # Add OK button
            ok_button = ttk.Button(main_frame, text="OK", command=status_dialog.destroy, width=10)
            ok_button.pack(pady=10)

            # Center the dialog on the screen
            status_dialog.update_idletasks()
            width = status_dialog.winfo_width()
            height = status_dialog.winfo_height()
            x = (status_dialog.winfo_screenwidth() // 2) - (width // 2)
            y = (status_dialog.winfo_screenheight() // 2) - (height // 2)
            status_dialog.geometry(f"{width}x{height}+{x}+{y}")

            # Wait for the dialog to be closed
            self.root.wait_window(status_dialog)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to get model status: {str(e)}")

    def system_info(self):
        """Display system information"""
        # Get system info
        system = platform.system()
        release = platform.release()
        version = platform.version()
        processor = platform.processor()

        # Get Python info
        python_version = sys.version

        # Get memory info
        memory_total = "N/A"
        memory_available = "N/A"
        memory_used = "N/A"
        memory_percent = "N/A"

        # Try to get memory info if psutil is available
        try:
            # Check if psutil is installed
            subprocess.run([sys.executable, '-c', 'import psutil'],
                         capture_output=True,
                         text=True,
                         encoding='utf-8')

            # If we get here, psutil is installed
            try:
                # Import psutil and get memory info
                import importlib
                psutil = importlib.import_module('psutil')
                memory = psutil.virtual_memory()
                memory_total = f"{memory.total / (1024 ** 3):.2f} GB"
                memory_available = f"{memory.available / (1024 ** 3):.2f} GB"
                memory_used = f"{memory.used / (1024 ** 3):.2f} GB"
                memory_percent = f"{memory.percent}%"
            except Exception:
                # If there's an error getting memory info, use default values
                pass
        except:
            # psutil is not installed, offer to install it
            install_psutil = messagebox.askyesno("Module Not Found",
                                              "The 'psutil' module is not installed. \n\nDo you want to install it now?")
            if install_psutil:
                try:
                    subprocess.run([sys.executable, '-m', 'pip', 'install', 'psutil'],
                                 check=True,
                                 capture_output=True,
                                 text=True,
                                 encoding='utf-8')
                    messagebox.showinfo("Installation Complete", "The 'psutil' module has been installed. Please restart the launcher.")
                except Exception as e:
                    messagebox.showerror("Installation Failed", f"Failed to install 'psutil': {str(e)}")

        # Get GPU info
        try:
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                # Extract GPU info
                lines = result.stdout.strip().split('\n')
                gpu_info = "NVIDIA GPU detected"
                for line in lines:
                    if "NVIDIA GeForce RTX" in line or "NVIDIA RTX" in line:
                        # Extract just the GPU model name without WDDM and other technical details
                        gpu_parts = line.strip().split()
                        for i, part in enumerate(gpu_parts):
                            if "RTX" in part and i+1 < len(gpu_parts):
                                gpu_info = f"NVIDIA GeForce {part} {gpu_parts[i+1]}"
                                break
                        break
            else:
                gpu_info = "No NVIDIA GPU detected"
        except FileNotFoundError:
            gpu_info = "NVIDIA GPU drivers not found"
        except Exception:
            gpu_info = "Could not detect GPU"

        # Create a custom dialog with better formatting
        info_dialog = tk.Toplevel(self.root)
        info_dialog.title("System Information")
        info_dialog.geometry("600x650")  # Increased height to accommodate all sections
        info_dialog.resizable(False, False)
        info_dialog.transient(self.root)  # Set as transient to main window
        info_dialog.grab_set()  # Make it modal

        # Set icon if available
        try:
            info_dialog.iconbitmap("icon.ico")
        except:
            pass

        # Create a frame with padding
        main_frame = ttk.Frame(info_dialog, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Add title
        title_label = ttk.Label(main_frame, text="System Information", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 15))

        # Create a frame for the system info with a border
        info_frame = ttk.LabelFrame(main_frame, text="Hardware & Software Details", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Using fixed width for all icons to ensure alignment

        # System info with icons/symbols
        system_frame = ttk.Frame(info_frame)
        system_frame.pack(fill=tk.X, pady=5)
        system_icon = ttk.Label(system_frame, text="🖥️", font=("Arial", 12), width=2, anchor="w")
        system_icon.pack(side=tk.LEFT, padx=(0, 10))
        system_label = ttk.Label(system_frame, text=f"System: {system} {release} {version}", font=("Arial", 10))
        system_label.pack(side=tk.LEFT)

        # Processor info
        cpu_frame = ttk.Frame(info_frame)
        cpu_frame.pack(fill=tk.X, pady=5)
        cpu_icon = ttk.Label(cpu_frame, text="⚙️", font=("Arial", 12), width=2, anchor="w")
        cpu_icon.pack(side=tk.LEFT, padx=(0, 10))
        cpu_label = ttk.Label(cpu_frame, text=f"Processor: {processor}", font=("Arial", 10))
        cpu_label.pack(side=tk.LEFT)

        # GPU info - Moved up right after CPU
        gpu_frame = ttk.Frame(info_frame)
        gpu_frame.pack(fill=tk.X, pady=5)
        gpu_icon = ttk.Label(gpu_frame, text="🎮", font=("Arial", 12), width=2, anchor="w")
        gpu_icon.pack(side=tk.LEFT, padx=(0, 10))

        # If the GPU info contains WDDM or other technical details, clean it up
        clean_gpu_info = gpu_info
        if "WDDM" in clean_gpu_info:
            clean_gpu_info = clean_gpu_info.split("WDDM")[0].strip()

        # Remove any version numbers or other technical details after the model number
        if "NVIDIA GeForce RTX" in clean_gpu_info:
            parts = clean_gpu_info.split()
            for i, part in enumerate(parts):
                if part.isdigit() and i > 0 and "RTX" in parts[i-1]:
                    clean_gpu_info = f"NVIDIA GeForce RTX {part}"
                    break

        # If after cleaning, the string is empty or just whitespace, use a default
        if not clean_gpu_info or clean_gpu_info.isspace():
            clean_gpu_info = "NVIDIA GeForce RTX 3060"

        gpu_label = ttk.Label(gpu_frame, text=f"GPU: {clean_gpu_info}", font=("Arial", 10), wraplength=500)  # Added wraplength to prevent cutoff
        gpu_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Python info
        python_frame = ttk.Frame(info_frame)
        python_frame.pack(fill=tk.X, pady=5)
        python_icon = ttk.Label(python_frame, text="🐍", font=("Arial", 12), width=2, anchor="w")
        python_icon.pack(side=tk.LEFT, padx=(0, 10))
        python_label = ttk.Label(python_frame, text=f"Python: {python_version.split()[0]}", font=("Arial", 10))
        python_label.pack(side=tk.LEFT)

        # Memory info section
        memory_section = ttk.LabelFrame(info_frame, text="Memory Information", padding=5)
        memory_section.pack(fill=tk.X, pady=10, padx=5)

        # Memory Total
        mem_total_frame = ttk.Frame(memory_section)
        mem_total_frame.pack(fill=tk.X, pady=2)
        mem_total_icon = ttk.Label(mem_total_frame, text="📊", font=("Arial", 12), width=2, anchor="w")
        mem_total_icon.pack(side=tk.LEFT, padx=(0, 10))
        mem_total_label = ttk.Label(mem_total_frame, text=f"Total: {memory_total}", font=("Arial", 10))
        mem_total_label.pack(side=tk.LEFT)

        # Memory Available
        mem_avail_frame = ttk.Frame(memory_section)
        mem_avail_frame.pack(fill=tk.X, pady=2)
        mem_avail_icon = ttk.Label(mem_avail_frame, text="✅", font=("Arial", 12), width=2, anchor="w")
        mem_avail_icon.pack(side=tk.LEFT, padx=(0, 10))
        mem_avail_label = ttk.Label(mem_avail_frame, text=f"Available: {memory_available}", font=("Arial", 10))
        mem_avail_label.pack(side=tk.LEFT)

        # Memory Used
        mem_used_frame = ttk.Frame(memory_section)
        mem_used_frame.pack(fill=tk.X, pady=2)
        mem_used_icon = ttk.Label(mem_used_frame, text="📈", font=("Arial", 12), width=2, anchor="w")
        mem_used_icon.pack(side=tk.LEFT, padx=(0, 10))
        mem_used_label = ttk.Label(mem_used_frame, text=f"Used: {memory_used} ({memory_percent})", font=("Arial", 10))
        mem_used_label.pack(side=tk.LEFT)

        # Add system requirements section
        sys_req_section = ttk.LabelFrame(main_frame, text="System Requirements for AI Models", padding=10)
        sys_req_section.pack(fill=tk.X, pady=10, padx=5)

        # GPU requirement
        gpu_req_frame = ttk.Frame(sys_req_section)
        gpu_req_frame.pack(fill=tk.X, pady=2)
        gpu_req_icon = ttk.Label(gpu_req_frame, text="🎮", font=("Arial", 12), width=2, anchor="w")
        gpu_req_icon.pack(side=tk.LEFT, padx=(0, 10))
        gpu_req_label = ttk.Label(gpu_req_frame, text="NVIDIA GPU with at least 4GB VRAM", font=("Arial", 10))
        gpu_req_label.pack(side=tk.LEFT)

        # OS requirement
        os_req_frame = ttk.Frame(sys_req_section)
        os_req_frame.pack(fill=tk.X, pady=2)
        os_req_icon = ttk.Label(os_req_frame, text="🖥️", font=("Arial", 12), width=2, anchor="w")
        os_req_icon.pack(side=tk.LEFT, padx=(0, 10))
        os_req_label = ttk.Label(os_req_frame, text="Windows 10 or later", font=("Arial", 10))
        os_req_label.pack(side=tk.LEFT)

        # RAM requirement
        ram_req_frame = ttk.Frame(sys_req_section)
        ram_req_frame.pack(fill=tk.X, pady=2)
        ram_req_icon = ttk.Label(ram_req_frame, text="📊", font=("Arial", 12), width=2, anchor="w")
        ram_req_icon.pack(side=tk.LEFT, padx=(0, 10))
        ram_req_label = ttk.Label(ram_req_frame, text="8GB+ RAM", font=("Arial", 10))
        ram_req_label.pack(side=tk.LEFT)

        # Python requirement
        python_req_frame = ttk.Frame(sys_req_section)
        python_req_frame.pack(fill=tk.X, pady=2)
        python_req_icon = ttk.Label(python_req_frame, text="🐍", font=("Arial", 12), width=2, anchor="w")
        python_req_icon.pack(side=tk.LEFT, padx=(0, 10))
        python_req_label = ttk.Label(python_req_frame, text="Python 3.8 or later", font=("Arial", 10))
        python_req_label.pack(side=tk.LEFT)

        # Add recommended models section
        req_section = ttk.LabelFrame(main_frame, text="Recommended AI Models", padding=10)
        req_section.pack(fill=tk.X, pady=10, padx=5)

        # Recommended models based on RTX 3060 (12GB VRAM)
        # First model recommendation
        model1_frame = ttk.Frame(req_section)
        model1_frame.pack(fill=tk.X, pady=2)
        model1_icon = ttk.Label(model1_frame, text="👍", font=("Arial", 12), width=2, anchor="w")  # Thumbs up emoji
        model1_icon.pack(side=tk.LEFT, padx=(0, 10))
        model1_label = ttk.Label(model1_frame, text="deepseek-coder:6.7b - Excellent for coding tasks", font=("Arial", 10))
        model1_label.pack(side=tk.LEFT)

        # Second model recommendation
        model2_frame = ttk.Frame(req_section)
        model2_frame.pack(fill=tk.X, pady=2)
        model2_icon = ttk.Label(model2_frame, text="👍", font=("Arial", 12), width=2, anchor="w")  # Thumbs up emoji
        model2_icon.pack(side=tk.LEFT, padx=(0, 10))
        model2_label = ttk.Label(model2_frame, text="llama2:13b - Great general purpose model", font=("Arial", 10))
        model2_label.pack(side=tk.LEFT)

        # Third model recommendation
        model3_frame = ttk.Frame(req_section)
        model3_frame.pack(fill=tk.X, pady=2)
        model3_icon = ttk.Label(model3_frame, text="👍", font=("Arial", 12), width=2, anchor="w")  # Thumbs up emoji
        model3_icon.pack(side=tk.LEFT, padx=(0, 10))
        model3_label = ttk.Label(model3_frame, text="mistral:7b - Fast and efficient assistant", font=("Arial", 10))
        model3_label.pack(side=tk.LEFT)

        # Fourth model recommendation
        model4_frame = ttk.Frame(req_section)
        model4_frame.pack(fill=tk.X, pady=2)
        model4_icon = ttk.Label(model4_frame, text="👍", font=("Arial", 12), width=2, anchor="w")  # Thumbs up emoji
        model4_icon.pack(side=tk.LEFT, padx=(0, 10))
        model4_label = ttk.Label(model4_frame, text="phi:3 - Compact but powerful model", font=("Arial", 10))
        model4_label.pack(side=tk.LEFT)

        # OK button
        ok_button = ttk.Button(main_frame, text="OK", command=info_dialog.destroy, width=10)
        ok_button.pack(pady=10)

        # Center the dialog on the screen
        info_dialog.update_idletasks()
        width = info_dialog.winfo_width()
        height = info_dialog.winfo_height()
        x = (info_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (info_dialog.winfo_screenheight() // 2) - (height // 2)
        info_dialog.geometry(f"{width}x{height}+{x}+{y}")

        # Wait for the dialog to be closed
        self.root.wait_window(info_dialog)

if __name__ == "__main__":
    # Check if we're being called with a URL
    if len(sys.argv) > 1 and sys.argv[1].startswith("ollama-start:"):
        # Parse the URL
        url = urllib.parse.urlparse(sys.argv[1])

        # Start Ollama
        try:
            # Check if Ollama is already running
            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=1)
                if response.status_code == 200:
                    print("Ollama is already running")
                    # Just exit, no need to start it again
                    sys.exit(0)
            except:
                # Ollama is not running, continue with starting it
                pass

            # Start Ollama
            process = subprocess.Popen(['ollama', 'serve'],
                           stdout=subprocess.PIPE,
                           stderr=subprocess.PIPE,
                           creationflags=subprocess.CREATE_NO_WINDOW)

            print("Started Ollama from URL handler")

            # Wait a bit to see if it starts successfully
            import time
            for _ in range(5):  # Try for 5 seconds
                time.sleep(1)
                try:
                    response = requests.get("http://localhost:11434/api/tags", timeout=1)
                    if response.status_code == 200:
                        print("Ollama started successfully")
                        break
                except:
                    pass

            # Create a small popup to confirm Ollama was started
            try:
                root = tk.Tk()
                root.withdraw()  # Hide the main window
                messagebox.showinfo("Ollama Started", "Ollama has been started successfully.")
                root.destroy()
            except:
                # If we can't show a popup, that's fine
                pass

        except Exception as e:
            print(f"Error starting Ollama: {e}")

            # Show error message
            try:
                root = tk.Tk()
                root.withdraw()  # Hide the main window
                messagebox.showerror("Error", f"Failed to start Ollama: {str(e)}\n\nPlease start Ollama manually.")
                root.destroy()
            except:
                # If we can't show a popup, that's fine
                pass
    else:
        try:
            # Start the API server in the background
            print("Starting API server in background...")
            api_server_thread = start_api_server_in_background()

            # Add a small delay to ensure the API server has time to start
            time.sleep(1)

            # Normal startup with error handling
            print("Initializing main application...")
            try:
                root = tk.Tk()
                # Set window title and icon
                root.title("DeepSeek R1 Launcher")
                try:
                    root.iconbitmap("icon.ico")
                except:
                    pass  # Icon not found, continue without it

                # Create the application instance
                app = DeepSeekLauncher(root)

                # Start the main event loop
                print("Starting main application...")
                root.mainloop()
            except Exception as e:
                print(f"Error in GUI initialization: {e}")
                import traceback
                traceback.print_exc()

                # Try to show an error message
                try:
                    messagebox.showerror("Error", f"Failed to start the application: {str(e)}")
                except:
                    print("Could not display error message dialog")
        except Exception as e:
            print(f"Critical error: {e}")
            import traceback
            traceback.print_exc()

