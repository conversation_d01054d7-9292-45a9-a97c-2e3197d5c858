# DeepSeek R1 Project - Reorganized Structure

This project has been reorganized into dedicated files for better maintainability while preserving all functionality.

## New File Organization

### Core Files

1. **`ollama_manager.py`** - All Ollama-related functionality
   - Installation and setup
   - Starting and stopping Ollama
   - Model management (download, list, etc.)
   - API server and proxy functionality

2. **`gui_interface.py`** - All GUI-related functionality
   - Main tkinter chat interface
   - Model selection and management
   - Chat history and user interaction
   - GPU detection and status updates

3. **`deepseek_launcher.py`** - Main application launcher and coordination
   - Dependency checking
   - System information
   - Launcher GUI with buttons for different functions
   - Coordination between different components

### Legacy Files (Updated)

- **`DeepSeek.py`** - Updated to use the new organized modules
- **`deepseek_r1_gui.py`** - Original GUI file (still functional)
- **`GUI.py`** - Original GUI file (still functional)
- **`Ollama.py`** - Renamed to `ollama_manager.py`
- **`deepseek_r1_launcher.py`** - Renamed to `deepseek_launcher.py`

## How to Use

### Option 1: Use the Main Launcher (Recommended)
```bash
python deepseek_launcher.py
```
This provides a GUI with buttons for all functions:
- Install Ollama
- Start Ollama
- Download AI Models
- Check Model Status
- System Information
- Run DeepSeek GUI
- Open Web Interface

### Option 2: Use Individual Components

#### Run the GUI Interface Directly
```bash
python gui_interface.py
```

#### Run Ollama Manager Directly
```bash
python ollama_manager.py [command]
```
Available commands:
- `install` - Install Ollama
- `start` - Start Ollama
- `proxy` - Start proxy server
- `api` - Start API server
- `batch` - Create batch file to start Ollama

#### Use the Legacy Entry Point
```bash
python DeepSeek.py
```

## Benefits of the New Organization

1. **Separation of Concerns**: Each file has a specific responsibility
   - `ollama_manager.py`: Ollama operations
   - `gui_interface.py`: User interface
   - `deepseek_launcher.py`: Application coordination

2. **Maintainability**: Code is easier to find, understand, and modify

3. **Reusability**: Components can be imported and used independently

4. **Same Functionality**: All original features are preserved

## File Dependencies

```
deepseek_launcher.py
├── ollama_manager.py
├── gui_interface.py
└── tkinter (built-in)

gui_interface.py
├── tkinter (built-in)
├── requests
└── subprocess

ollama_manager.py
├── requests
├── subprocess
└── http.server (built-in)
```

## Requirements

- Python 3.8+
- tkinter (usually included with Python)
- requests
- Ollama (installed separately)

The launcher will automatically check and install missing Python packages.
